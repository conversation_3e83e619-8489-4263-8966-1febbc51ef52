#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
semantic_chunker.py

语义分块器 - 实现AI驱动的智能内容分块
Stage 1: AI预处理，在源Markdown内容中插入语义分块标记
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class ChunkingResult:
    """分块结果数据类"""
    success: bool
    preprocessed_content: str
    chunk_count: int
    processing_time: float
    error_message: str = ""
    original_length: int = 0
    preprocessed_length: int = 0


class SemanticChunker:
    """
    语义分块器
    
    使用AI分析文档内容，在语义边界处插入分块标记
    """
    
    def __init__(self, ai_service, chunk_marker: str = "<!-- CHUNK_BREAK -->"):
        """
        初始化语义分块器
        
        Args:
            ai_service: AI服务实例
            chunk_marker: 分块标记字符串
        """
        self.ai_service = ai_service
        self.chunk_marker = chunk_marker
        
        # 语义分块的系统提示词
        self.system_prompt = """你是一个专业的文档内容分析师，专门负责将长文档分割成语义完整的块。

你的任务是：
1. 分析输入的Markdown文档内容
2. 识别语义边界（完整的问答对）
3. 在适当的位置插入分块标记 `<!-- CHUNK_BREAK -->`
4. 仅仅选择一个（只需要插入一个！！！必须插入一个！！！！）合适为插入分块标记即可。这是一个大段文本的一个部分。

分块原则：
- 保持问答对的完整性，不要在问题和答案之间分割
- 保持相关概念的连贯性
- 每个块应该包含足够的上下文信息
- 保持原始格式和结构不变

输出要求：
- 直接输出处理后的Markdown内容
- 只在语义边界处添加 `<!-- CHUNK_BREAK -->` 标记
- 不要添加任何其他说明或注释
- 保持原始内容的完整性和格式"""

        self.task_prompt = """请分析以下Markdown文档内容，在适当的语义边界处插入分块标记 `<!-- CHUNK_BREAK -->`：

---

{content}

---

请直接输出处理后的内容，在语义边界处插入 `<!-- CHUNK_BREAK -->` 标记。"""

        logging.info(f"语义分块器初始化完成，分块标记: {self.chunk_marker}")
    
    def preprocess_document(self, content: str, project_name: str) -> ChunkingResult:
        """
        预处理文档，插入语义分块标记

        Args:
            content: 原始Markdown内容
            project_name: 项目名称

        Returns:
            ChunkingResult: 分块结果
        """
        start_time = time.time()
        original_length = len(content)

        logging.info(f"开始语义分块预处理: 项目={project_name}, 原始长度={original_length}字符")

        # 检查内容长度
        if original_length == 0:
            return ChunkingResult(
                success=False,
                preprocessed_content="",
                chunk_count=0,
                processing_time=0,
                error_message="输入内容为空",
                original_length=0,
                preprocessed_length=0
            )

        # 如果内容较短，不需要分块
        if original_length < 6000:  # 提高阈值，小于6000字符不分块
            logging.info(f"内容较短({original_length}字符)，跳过语义分块")
            return ChunkingResult(
                success=True,
                preprocessed_content=content,
                chunk_count=1,
                processing_time=time.time() - start_time,
                original_length=original_length,
                preprocessed_length=original_length
            )

        try:
            # 使用智能边界分析进行语义分块
            preprocessed_content = self._intelligent_semantic_chunking(content, project_name)

            # 计算分块数量
            chunk_count = self._count_chunks(preprocessed_content)

            processing_time = time.time() - start_time
            preprocessed_length = len(preprocessed_content)

            logging.info(f"语义分块完成: 生成{chunk_count}个块, 处理时间{processing_time:.2f}s")

            return ChunkingResult(
                success=True,
                preprocessed_content=preprocessed_content,
                chunk_count=chunk_count,
                processing_time=processing_time,
                original_length=original_length,
                preprocessed_length=preprocessed_length
            )

        except Exception as e:
            error_msg = f"语义分块处理异常: {str(e)}"
            logging.error(error_msg)

            return ChunkingResult(
                success=False,
                preprocessed_content=content,
                chunk_count=1,
                processing_time=time.time() - start_time,
                error_message=error_msg,
                original_length=original_length,
                preprocessed_length=original_length
            )
    
    def _intelligent_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        智能语义分块 - 使用AI分析边界区域

        Args:
            content: 原始内容
            project_name: 项目名称

        Returns:
            str: 插入分块标记后的内容
        """
        lines = content.split('\n')
        result_lines = []
        current_chunk_size = 0
        target_chunk_size = 12000  # 目标块大小（12K字符）
        boundary_context_size = 1500  # 边界上下文大小（3K字符的一半）

        logging.info(f"开始智能语义分块: 目标块大小={target_chunk_size}, 边界上下文={boundary_context_size*2}")

        for i, line in enumerate(lines):
            result_lines.append(line)
            current_chunk_size += len(line) + 1  # +1 for newline

            # 检查是否需要考虑分块
            if current_chunk_size > target_chunk_size:
                # 寻找合适的分割点
                break_point = self._find_optimal_break_point(lines, i, boundary_context_size, project_name)

                if break_point is not None and break_point > len(result_lines) - 100:  # 确保不会回退太远
                    # 在找到的分割点插入标记
                    insert_position = break_point - (len(lines) - len(result_lines)) + 1
                    if 0 <= insert_position < len(result_lines):
                        result_lines.insert(insert_position, '')
                        result_lines.insert(insert_position + 1, self.chunk_marker)
                        result_lines.insert(insert_position + 2, '')
                        current_chunk_size = sum(len(line) + 1 for line in result_lines[insert_position + 3:])
                        logging.info(f"在第{break_point}行插入分块标记")
                    else:
                        # 如果位置无效，使用简单规则
                        if self._is_good_break_point(line, lines, i):
                            result_lines.extend(['', self.chunk_marker, ''])
                            current_chunk_size = 0
                else:
                    # 如果AI分析失败，使用简单规则
                    if self._is_good_break_point(line, lines, i):
                        result_lines.extend(['', self.chunk_marker, ''])
                        current_chunk_size = 0

        return '\n'.join(result_lines)

    def _find_optimal_break_point(self, lines: List[str], current_line: int, context_size: int, project_name: str) -> Optional[int]:
        """
        使用AI找到最佳分割点

        Args:
            lines: 所有行
            current_line: 当前行索引
            context_size: 上下文大小（字符数）
            project_name: 项目名称

        Returns:
            Optional[int]: 最佳分割点的行号，如果失败返回None
        """
        try:
            # 确定分析范围
            start_char = max(0, sum(len(line) + 1 for line in lines[:current_line]) - context_size)
            end_char = min(len('\n'.join(lines)),
                          sum(len(line) + 1 for line in lines[:current_line + 1]) + context_size)

            # 提取边界区域内容
            full_content = '\n'.join(lines)
            boundary_content = full_content[start_char:end_char]

            # 构建AI分析提示词
            analysis_prompt = f"""请分析以下文档片段，找到最佳的语义分割点。

这是一个长文档的边界区域（约3000字符），需要在语义完整的地方进行分割。

分析原则：
1. 保持问答对的完整性
2. 保持相关概念的连贯性
3. 在段落或章节的自然边界处分割
4. 避免在句子中间分割

请在最佳分割位置插入标记 `{self.chunk_marker}`，只插入一个标记。

内容：
---
{boundary_content}
---

请直接输出处理后的内容，在最佳语义边界处插入 `{self.chunk_marker}` 标记。"""

            # 调用AI服务
            logging.info(f"调用AI分析边界区域: {len(boundary_content)}字符")

            # 使用AI服务的直接调用方法
            response = self._call_ai_for_boundary_analysis(analysis_prompt, project_name)

            if response and self.chunk_marker in response:
                # 找到标记在原始内容中的位置
                marker_pos = response.find(self.chunk_marker)
                if marker_pos != -1:
                    # 计算在原始行中的位置
                    before_marker = response[:marker_pos]
                    char_offset = start_char + len(before_marker.encode('utf-8'))

                    # 转换为行号
                    line_offset = full_content[:char_offset].count('\n')
                    return line_offset

            return None

        except Exception as e:
            logging.error(f"AI边界分析失败: {e}")
            return None

    def _call_ai_for_boundary_analysis(self, prompt: str, project_name: str) -> Optional[str]:
        """
        调用AI服务进行边界分析

        Args:
            prompt: 分析提示词
            project_name: 项目名称

        Returns:
            Optional[str]: AI响应内容
        """
        try:
            # 使用新的直接调用方法
            if hasattr(self.ai_service, 'call_api_with_response'):
                response = self.ai_service.call_api_with_response(prompt, project_name)
                if response:
                    logging.info(f"AI边界分析成功，响应长度: {len(response)}")
                    return response
                else:
                    logging.warning("AI边界分析返回空响应")
                    return None
            else:
                logging.warning("AI服务不支持直接内容返回，跳过AI边界分析")
                return None

        except Exception as e:
            logging.error(f"AI边界分析调用失败: {e}")
            return None
    
    def _is_good_break_point(self, line: str, lines: List[str], index: int) -> bool:
        """判断是否是好的分割点"""
        # 在段落结束后分割
        if line.strip() == '' and index + 1 < len(lines) and lines[index + 1].strip() != '':
            return True
        
        # 在标题前分割
        if index + 1 < len(lines) and lines[index + 1].startswith('#'):
            return True
        
        # 在问答对之间分割（简单启发式）
        if (line.strip().endswith('？') or line.strip().endswith('?') or 
            line.strip().endswith('。') or line.strip().endswith('.')):
            if index + 1 < len(lines) and lines[index + 1].strip() != '':
                return True
        
        return False
    
    def _count_chunks(self, content: str) -> int:
        """计算分块数量"""
        return content.count(self.chunk_marker) + 1
    
    def get_preprocessed_file_path(self, project_name: str, base_dir: Path) -> Path:
        """
        获取预处理文件路径
        
        Args:
            project_name: 项目名称
            base_dir: 基础目录
            
        Returns:
            Path: 预处理文件路径
        """
        project_dir = base_dir / "extracted" / project_name
        
        # 查找原始markdown文件
        md_files = list(project_dir.glob("*.md"))
        if not md_files:
            raise FileNotFoundError(f"项目 {project_name} 中没有找到markdown文件")
        
        # 使用第一个markdown文件作为基础
        original_md = md_files[0]
        
        # 生成预处理文件名
        preprocessed_name = original_md.stem + "_preprocessed.md"
        return project_dir / preprocessed_name
    
    def save_preprocessed_content(self, content: str, file_path: Path) -> bool:
        """
        保存预处理内容到文件
        
        Args:
            content: 预处理后的内容
            file_path: 保存路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logging.info(f"预处理内容已保存到: {file_path}")
            return True
            
        except Exception as e:
            logging.error(f"保存预处理内容失败: {e}")
            return False
    
    def load_preprocessed_content(self, file_path: Path) -> Optional[str]:
        """
        加载预处理内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 预处理内容，如果失败返回None
        """
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logging.info(f"已加载预处理内容: {file_path}")
            return content
            
        except Exception as e:
            logging.error(f"加载预处理内容失败: {e}")
            return None
    
    def is_preprocessed_file_valid(self, file_path: Path, original_file_path: Path) -> bool:
        """
        检查预处理文件是否有效（比原始文件新）
        
        Args:
            file_path: 预处理文件路径
            original_file_path: 原始文件路径
            
        Returns:
            bool: 是否有效
        """
        try:
            if not file_path.exists() or not original_file_path.exists():
                return False
            
            preprocessed_mtime = file_path.stat().st_mtime
            original_mtime = original_file_path.stat().st_mtime
            
            return preprocessed_mtime > original_mtime
            
        except Exception as e:
            logging.error(f"检查预处理文件有效性失败: {e}")
            return False


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 模拟AI服务
    class MockAIService:
        def __init__(self):
            self.config = {}
        
        def call_api(self, chunk, idx, cache_dir):
            return True
    
    # 创建语义分块器
    ai_service = MockAIService()
    chunker = SemanticChunker(ai_service)
    
    # 测试内容
    test_content = """# 测试文档

这是第一个段落，包含一些基本信息。

## 问题1
这是第一个问题的描述？

这是第一个问题的答案。答案包含详细的解释和说明。

## 问题2
这是第二个问题的描述？

这是第二个问题的答案。答案也包含详细的解释。

## 总结
这是文档的总结部分。
""" * 10  # 重复内容使其足够长
    
    # 执行语义分块
    result = chunker.preprocess_document(test_content, "test_project")
    
    print(f"分块结果: 成功={result.success}")
    print(f"原始长度: {result.original_length}")
    print(f"处理后长度: {result.preprocessed_length}")
    print(f"分块数量: {result.chunk_count}")
    print(f"处理时间: {result.processing_time:.2f}s")
    
    if result.chunk_count > 1:
        print("\n分块标记位置:")
        lines = result.preprocessed_content.split('\n')
        for i, line in enumerate(lines):
            if chunker.chunk_marker in line:
                print(f"  第 {i+1} 行: {line}")
